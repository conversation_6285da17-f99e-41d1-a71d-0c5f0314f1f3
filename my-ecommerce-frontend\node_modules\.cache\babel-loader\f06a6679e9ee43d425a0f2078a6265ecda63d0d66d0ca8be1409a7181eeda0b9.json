{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\Toast.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport '../styles/Toast.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Toast({\n  message,\n  type = 'success',\n  duration = 3000,\n  onClose\n}) {\n  _s();\n  const [isVisible, setIsVisible] = useState(true);\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(false);\n      setTimeout(() => {\n        onClose();\n      }, 300); // Wait for fade out animation\n    }, duration);\n    return () => clearTimeout(timer);\n  }, [duration, onClose]);\n  const handleClose = () => {\n    setIsVisible(false);\n    setTimeout(() => {\n      onClose();\n    }, 300);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `toast ${type} ${isVisible ? 'show' : 'hide'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toast-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"toast-message\",\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"toast-close\",\n        onClick: handleClose,\n        children: \"\\xD7\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n}\n_s(Toast, \"m22S9IQwDfEe/fCJY7LYj8YPDMo=\");\n_c = Toast;\nexport default Toast;\nvar _c;\n$RefreshReg$(_c, \"Toast\");", "map": {"version": 3, "names": ["useState", "useEffect", "jsxDEV", "_jsxDEV", "Toast", "message", "type", "duration", "onClose", "_s", "isVisible", "setIsVisible", "timer", "setTimeout", "clearTimeout", "handleClose", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/Toast.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport '../styles/Toast.css';\n\nfunction Toast({ message, type = 'success', duration = 3000, onClose }) {\n  const [isVisible, setIsVisible] = useState(true);\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setIsVisible(false);\n      setTimeout(() => {\n        onClose();\n      }, 300); // Wait for fade out animation\n    }, duration);\n\n    return () => clearTimeout(timer);\n  }, [duration, onClose]);\n\n  const handleClose = () => {\n    setIsVisible(false);\n    setTimeout(() => {\n      onClose();\n    }, 300);\n  };\n\n  return (\n    <div className={`toast ${type} ${isVisible ? 'show' : 'hide'}`}>\n      <div className=\"toast-content\">\n        <span className=\"toast-message\">{message}</span>\n        <button className=\"toast-close\" onClick={handleClose}>\n          ×\n        </button>\n      </div>\n    </div>\n  );\n}\n\nexport default Toast;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,SAASC,KAAKA,CAAC;EAAEC,OAAO;EAAEC,IAAI,GAAG,SAAS;EAAEC,QAAQ,GAAG,IAAI;EAAEC;AAAQ,CAAC,EAAE;EAAAC,EAAA;EACtE,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACd,MAAMW,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BF,YAAY,CAAC,KAAK,CAAC;MACnBE,UAAU,CAAC,MAAM;QACfL,OAAO,CAAC,CAAC;MACX,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,EAAED,QAAQ,CAAC;IAEZ,OAAO,MAAMO,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,CAACL,QAAQ,EAAEC,OAAO,CAAC,CAAC;EAEvB,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxBJ,YAAY,CAAC,KAAK,CAAC;IACnBE,UAAU,CAAC,MAAM;MACfL,OAAO,CAAC,CAAC;IACX,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,oBACEL,OAAA;IAAKa,SAAS,EAAE,SAASV,IAAI,IAAII,SAAS,GAAG,MAAM,GAAG,MAAM,EAAG;IAAAO,QAAA,eAC7Dd,OAAA;MAAKa,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5Bd,OAAA;QAAMa,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEZ;MAAO;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAChDlB,OAAA;QAAQa,SAAS,EAAC,aAAa;QAACM,OAAO,EAAEP,WAAY;QAAAE,QAAA,EAAC;MAEtD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACZ,EAAA,CA/BQL,KAAK;AAAAmB,EAAA,GAALnB,KAAK;AAiCd,eAAeA,KAAK;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}