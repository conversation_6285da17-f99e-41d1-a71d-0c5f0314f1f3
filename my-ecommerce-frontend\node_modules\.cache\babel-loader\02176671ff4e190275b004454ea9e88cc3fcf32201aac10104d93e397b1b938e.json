{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\ProductDetails.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport Button from './Button.js';\nimport { useQuery, useMutation } from '@apollo/client';\nimport { GET_PRODUCT_DETAILS } from '../graphql/queries';\nimport { ADD_TO_CART_MUTATION } from '../graphql/mutations.js';\nimport { GET_CART_QUERY } from '../graphql/queries';\nimport { useParams } from 'react-router-dom';\nimport { useToast } from '../contexts/ToastContext';\nimport '../styles/ProductDetails.css';\nimport parse from \"html-react-parser\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ProductDetails() {\n  _s();\n  const {\n    id\n  } = useParams();\n  const {\n    loading,\n    error,\n    data: product\n  } = useQuery(GET_PRODUCT_DETAILS, {\n    variables: {\n      id: id\n    }\n  });\n  const [productAttributes, setAttributes] = useState(null);\n  const [selectedOptions, setSelectedOptions] = useState(null);\n  const {\n    showSuccess,\n    showError\n  } = useToast();\n  const [addToCart] = useMutation(ADD_TO_CART_MUTATION, {\n    refetchQueries: [{\n      query: GET_CART_QUERY\n    }],\n    awaitRefetchQueries: true,\n    onCompleted: data => {\n      console.log('Product added to cart successfully:', data);\n      showSuccess('Product added to cart!');\n    },\n    onError: error => {\n      console.error('Add to cart error:', error);\n      showError('Failed to add product to cart. Please try again.');\n    },\n    errorPolicy: 'all'\n  });\n  useEffect(() => {\n    if (product && product.product.attributes) {\n      console.log('Raw attributes:', product.attributes);\n      try {\n        const parsedAttributes = JSON.parse(product.product.attributes);\n        console.log('Parsed attributes:', parsedAttributes);\n        const associativeArray = Object.entries(parsedAttributes).map(([key, value]) => ({\n          key,\n          value\n        }));\n        console.log('Final attributes array:', associativeArray);\n        setAttributes(associativeArray);\n      } catch (error) {\n        console.error('Error parsing attributes:', error);\n        setAttributes([]);\n      }\n    } else {\n      console.log('No attributes in product data, testing direct GraphQL fetch');\n\n      // Test direct GraphQL fetch\n      if (product && id) {\n        const testGraphQL = async () => {\n          try {\n            const response = await fetch('http://localhost:8000/graphql.php', {\n              method: 'POST',\n              headers: {\n                'Content-Type': 'application/json'\n              },\n              body: JSON.stringify({\n                query: `{ product(id: \"${id}\") { id name attributes } }`\n              })\n            });\n            const result = await response.json();\n            console.log('Direct GraphQL test result:', result);\n            if (result.data && result.data.product && result.data.product.attributes) {\n              const parsedAttributes = JSON.parse(result.data.product.attributes);\n              const associativeArray = Object.entries(parsedAttributes).map(([key, value]) => ({\n                key,\n                value\n              }));\n              console.log('Setting attributes from direct fetch:', associativeArray);\n              setAttributes(associativeArray);\n            } else {\n              console.log('Setting hardcoded attributes as fallback');\n              setAttributes([{\n                key: 'Color',\n                value: ['#FF0000', '#00FF00', '#0000FF', '#000000', '#FFFFFF']\n              }, {\n                key: 'Capacity',\n                value: ['512G', '1T']\n              }]);\n            }\n          } catch (error) {\n            setAttributes([{\n              key: 'Color',\n              value: ['#FF0000', '#00FF00', '#0000FF', '#000000', '#FFFFFF']\n            }, {\n              key: 'Capacity',\n              value: ['512G', '1T']\n            }]);\n          }\n        };\n        testGraphQL();\n      }\n    }\n  }, [product, id]);\n  const handleOptionSelect = (attributeKey, optionValue) => {\n    setSelectedOptions(prev => ({\n      ...prev,\n      [attributeKey]: optionValue\n    }));\n  };\n\n  // Helper function to convert attribute name to kebab case\n  const toKebabCase = str => {\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n  };\n  const handleAddToCart = () => {\n    var _product$product;\n    const productId = product === null || product === void 0 ? void 0 : (_product$product = product.product) === null || _product$product === void 0 ? void 0 : _product$product.id;\n    if (!productId) {\n      alert('Cannot add to cart: Product ID is missing');\n      return;\n    }\n    addToCart({\n      variables: {\n        productId: productId,\n        quantity: 1\n      }\n    });\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: [\"Error: \", error.message]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 123,\n    columnNumber: 21\n  }, this);\n  if (!product.product) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Product not found\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 32\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      \"data-testid\": \"product-gallery\",\n      className: \"gallary\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-image\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"product-card__image\",\n          src: product.product.image_url,\n          alt: product.product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      \"data-testid\": \"product-description\",\n      className: \"details\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"product-name\",\n        children: product.product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"product-brand\",\n        children: product.product.brand\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: productAttributes && productAttributes.length > 0 ? productAttributes.map(attribute => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"attribute\",\n          \"data-testid\": `product-attribute-${toKebabCase(attribute['key'])}`,\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"attribute-name\",\n            children: [attribute['key'], \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"attribute-values\",\n            children: attribute['value'] && attribute['value'].map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `attribute-value-btn ${selectedOptions && selectedOptions[attribute['key']] === item ? 'selected' : ''}`,\n              onClick: () => {\n                if (attribute['key']) handleOptionSelect(attribute['key'], item);\n              },\n              style: {\n                backgroundColor: attribute['key'] === \"Color\" ? item : ''\n              },\n              children: attribute['key'] === \"Color\" ? '' : item\n            }, item, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 25\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 19\n          }, this)]\n        }, attribute['key'], true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 17\n        }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No attributes available for this product.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"price\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"product-price\",\n          children: [product.product.amount, \"$\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          \"data-testid\": \"add-to-cart\",\n          className: \"add-to-cart\",\n          text: \"Add To Cart\",\n          onClick: handleAddToCart\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"description\",\n          children: product.product.description ? parse(product.product.description) : /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No description available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 83\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n}\n_s(ProductDetails, \"AR56EWyxMe/PTDCdzuunCLugDDM=\", false, function () {\n  return [useParams, useQuery, useToast, useMutation];\n});\n_c = ProductDetails;\nexport default ProductDetails;\nvar _c;\n$RefreshReg$(_c, \"ProductDetails\");", "map": {"version": 3, "names": ["useState", "useEffect", "<PERSON><PERSON>", "useQuery", "useMutation", "GET_PRODUCT_DETAILS", "ADD_TO_CART_MUTATION", "GET_CART_QUERY", "useParams", "useToast", "parse", "jsxDEV", "_jsxDEV", "ProductDetails", "_s", "id", "loading", "error", "data", "product", "variables", "productAttributes", "setAttributes", "selectedOptions", "setSelectedOptions", "showSuccess", "showError", "addToCart", "refetchQueries", "query", "awaitRefetchQueries", "onCompleted", "console", "log", "onError", "errorPolicy", "attributes", "parsedAttributes", "JSON", "associativeArray", "Object", "entries", "map", "key", "value", "testGraphQL", "response", "fetch", "method", "headers", "body", "stringify", "result", "json", "handleOptionSelect", "<PERSON><PERSON><PERSON>", "optionValue", "prev", "toKebabCase", "str", "toLowerCase", "replace", "handleAddToCart", "_product$product", "productId", "alert", "quantity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "className", "src", "image_url", "alt", "name", "brand", "length", "attribute", "item", "onClick", "style", "backgroundColor", "amount", "text", "description", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/ProductDetails.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport Button from './Button.js';\r\nimport { useQuery, useMutation } from '@apollo/client';\r\nimport { GET_PRODUCT_DETAILS } from '../graphql/queries';\r\nimport { ADD_TO_CART_MUTATION } from '../graphql/mutations.js'\r\nimport { GET_CART_QUERY } from '../graphql/queries'\r\nimport { useParams } from 'react-router-dom';\r\nimport { useToast } from '../contexts/ToastContext';\r\nimport '../styles/ProductDetails.css';\r\nimport parse from \"html-react-parser\";\r\n\r\n\r\nfunction ProductDetails() {\r\n  const { id } = useParams();\r\n  const { loading, error, data: product } = useQuery(GET_PRODUCT_DETAILS, {\r\n    variables: { id: id },\r\n  });\r\n  const [productAttributes, setAttributes] = useState(null);\r\n  const [selectedOptions, setSelectedOptions] = useState(null);\r\n  const { showSuccess, showError } = useToast();\r\n\r\n  const [addToCart] = useMutation(ADD_TO_CART_MUTATION, {\r\n    refetchQueries: [{ query: GET_CART_QUERY }],\r\n    awaitRefetchQueries: true,\r\n    onCompleted: (data) => {\r\n      console.log('Product added to cart successfully:', data);\r\n      showSuccess('Product added to cart!');\r\n    },\r\n    onError: (error) => {\r\n      console.error('Add to cart error:', error);\r\n      showError('Failed to add product to cart. Please try again.');\r\n    },\r\n    errorPolicy: 'all'\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (product && product.product.attributes) {\r\n      console.log('Raw attributes:', product.attributes);\r\n      try {\r\n        const parsedAttributes = JSON.parse(product.product.attributes);\r\n        console.log('Parsed attributes:', parsedAttributes);\r\n        const associativeArray = Object.entries(parsedAttributes).map(([key, value]) => ({ key, value }));\r\n        console.log('Final attributes array:', associativeArray);\r\n        setAttributes(associativeArray);\r\n      } catch (error) {\r\n        console.error('Error parsing attributes:', error);\r\n        setAttributes([]);\r\n      }\r\n    } else {\r\n      console.log('No attributes in product data, testing direct GraphQL fetch');\r\n    \r\n            // Test direct GraphQL fetch\r\n      if (product && id) {\r\n        const testGraphQL = async () => {\r\n          try {\r\n            const response = await fetch('http://localhost:8000/graphql.php', {\r\n              method: 'POST',\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n              body: JSON.stringify({\r\n                query: `{ product(id: \"${id}\") { id name attributes } }`\r\n              })\r\n            });\r\n\r\n            const result = await response.json();\r\n            console.log('Direct GraphQL test result:', result);\r\n\r\n            if (result.data && result.data.product && result.data.product.attributes) {\r\n              const parsedAttributes = JSON.parse(result.data.product.attributes);\r\n              const associativeArray = Object.entries(parsedAttributes).map(([key, value]) => ({ key, value }));\r\n              console.log('Setting attributes from direct fetch:', associativeArray);\r\n              setAttributes(associativeArray);\r\n            } else {\r\n              console.log('Setting hardcoded attributes as fallback');\r\n              setAttributes([\r\n                { key: 'Color', value: ['#FF0000', '#00FF00', '#0000FF', '#000000', '#FFFFFF'] },\r\n                { key: 'Capacity', value: ['512G', '1T'] }\r\n              ]);\r\n            }\r\n          } catch (error) {\r\n            setAttributes([\r\n              { key: 'Color', value: ['#FF0000', '#00FF00', '#0000FF', '#000000', '#FFFFFF'] },\r\n              { key: 'Capacity', value: ['512G', '1T'] }\r\n            ]);\r\n          }\r\n        };\r\n\r\n        testGraphQL();\r\n      }\r\n    }\r\n  }, [product, id]);\r\n\r\n  const handleOptionSelect = (attributeKey, optionValue) => {\r\n    setSelectedOptions((prev) => ({\r\n      ...prev,\r\n      [attributeKey]: optionValue,\r\n    }));\r\n  };\r\n\r\n  // Helper function to convert attribute name to kebab case\r\n  const toKebabCase = (str) => {\r\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\r\n  };\r\n\r\n  const handleAddToCart = () => {\r\n    const productId = product?.product?.id;\r\n\r\n    if (!productId) {\r\n      alert('Cannot add to cart: Product ID is missing');\r\n      return;\r\n    }\r\n\r\n    addToCart({\r\n      variables: {\r\n        productId: productId,\r\n        quantity: 1,\r\n      },\r\n    });\r\n  };\r\n\r\n  if (loading) return <p>Loading...</p>;\r\n  if (error) return <p>Error: {error.message}</p>;\r\n\r\n  if (!product.product) return <p>Product not found</p>;\r\n\r\n  return (\r\n    <div className=\"product-page\">\r\n      <div data-testid='product-gallery' className=\"gallary\">\r\n        <div className=\"main-image\">\r\n          <img className=\"product-card__image\" src={product.product.image_url} alt={product.product.name}/>\r\n        </div>\r\n      </div>\r\n\r\n      <div data-testid='product-description' className=\"details\">\r\n        <h1 className=\"product-name\">{product.product.name}</h1>\r\n        <p className=\"product-brand\">{product.product.brand}</p>\r\n        <div>\r\n            {productAttributes && productAttributes.length > 0 ? (\r\n              productAttributes.map((attribute) => (\r\n                <div className=\"attribute\" key={attribute['key']} data-testid={`product-attribute-${toKebabCase(attribute['key'])}`}>\r\n                  <p className=\"attribute-name\">{attribute['key']}:</p>\r\n                  <div className=\"attribute-values\">\r\n                    {attribute['value'] && attribute['value'].map(item => (\r\n                        <button\r\n                        key={item}\r\n                        className={`attribute-value-btn ${selectedOptions && selectedOptions[attribute['key']] === item ? 'selected' : ''}`}\r\n                        onClick={() =>{if (attribute['key']) handleOptionSelect(attribute['key'], item)}}\r\n                        style={{backgroundColor: attribute['key'] === \"Color\" ? item : ''}}\r\n                      >\r\n                        {attribute['key'] === \"Color\" ? '' : item}\r\n                      </button>\r\n                    ))\r\n                    }\r\n                  </div>\r\n                </div>\r\n              ))\r\n            ) : (\r\n              <p>No attributes available for this product.</p>\r\n            )}\r\n          </div>\r\n        <div className=\"price\">\r\n        <p className=\"product-price\">{product.product.amount}$</p>\r\n        </div>\r\n        <div>\r\n           <Button data-testid='add-to-cart' className='add-to-cart' text=\"Add To Cart\" onClick={handleAddToCart} />\r\n            <div className=\"description\">\r\n              {product.product.description ? parse(product.product.description) : <p>No description available</p>}\r\n            </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProductDetails;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,gBAAgB;AACtD,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,8BAA8B;AACrC,OAAOC,KAAK,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGtC,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAG,CAAC,GAAGP,SAAS,CAAC,CAAC;EAC1B,MAAM;IAAEQ,OAAO;IAAEC,KAAK;IAAEC,IAAI,EAAEC;EAAQ,CAAC,GAAGhB,QAAQ,CAACE,mBAAmB,EAAE;IACtEe,SAAS,EAAE;MAAEL,EAAE,EAAEA;IAAG;EACtB,CAAC,CAAC;EACF,MAAM,CAACM,iBAAiB,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACzD,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM;IAAEyB,WAAW;IAAEC;EAAU,CAAC,GAAGjB,QAAQ,CAAC,CAAC;EAE7C,MAAM,CAACkB,SAAS,CAAC,GAAGvB,WAAW,CAACE,oBAAoB,EAAE;IACpDsB,cAAc,EAAE,CAAC;MAAEC,KAAK,EAAEtB;IAAe,CAAC,CAAC;IAC3CuB,mBAAmB,EAAE,IAAI;IACzBC,WAAW,EAAGb,IAAI,IAAK;MACrBc,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEf,IAAI,CAAC;MACxDO,WAAW,CAAC,wBAAwB,CAAC;IACvC,CAAC;IACDS,OAAO,EAAGjB,KAAK,IAAK;MAClBe,OAAO,CAACf,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CS,SAAS,CAAC,kDAAkD,CAAC;IAC/D,CAAC;IACDS,WAAW,EAAE;EACf,CAAC,CAAC;EAEFlC,SAAS,CAAC,MAAM;IACd,IAAIkB,OAAO,IAAIA,OAAO,CAACA,OAAO,CAACiB,UAAU,EAAE;MACzCJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEd,OAAO,CAACiB,UAAU,CAAC;MAClD,IAAI;QACF,MAAMC,gBAAgB,GAAGC,IAAI,CAAC5B,KAAK,CAACS,OAAO,CAACA,OAAO,CAACiB,UAAU,CAAC;QAC/DJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,gBAAgB,CAAC;QACnD,MAAME,gBAAgB,GAAGC,MAAM,CAACC,OAAO,CAACJ,gBAAgB,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,MAAM;UAAED,GAAG;UAAEC;QAAM,CAAC,CAAC,CAAC;QACjGZ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEM,gBAAgB,CAAC;QACxDjB,aAAa,CAACiB,gBAAgB,CAAC;MACjC,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACde,OAAO,CAACf,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDK,aAAa,CAAC,EAAE,CAAC;MACnB;IACF,CAAC,MAAM;MACLU,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;;MAEpE;MACN,IAAId,OAAO,IAAIJ,EAAE,EAAE;QACjB,MAAM8B,WAAW,GAAG,MAAAA,CAAA,KAAY;UAC9B,IAAI;YACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAE;cAChEC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE;gBACP,cAAc,EAAE;cAClB,CAAC;cACDC,IAAI,EAAEZ,IAAI,CAACa,SAAS,CAAC;gBACnBtB,KAAK,EAAE,kBAAkBd,EAAE;cAC7B,CAAC;YACH,CAAC,CAAC;YAEF,MAAMqC,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;YACpCrB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmB,MAAM,CAAC;YAElD,IAAIA,MAAM,CAAClC,IAAI,IAAIkC,MAAM,CAAClC,IAAI,CAACC,OAAO,IAAIiC,MAAM,CAAClC,IAAI,CAACC,OAAO,CAACiB,UAAU,EAAE;cACxE,MAAMC,gBAAgB,GAAGC,IAAI,CAAC5B,KAAK,CAAC0C,MAAM,CAAClC,IAAI,CAACC,OAAO,CAACiB,UAAU,CAAC;cACnE,MAAMG,gBAAgB,GAAGC,MAAM,CAACC,OAAO,CAACJ,gBAAgB,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,MAAM;gBAAED,GAAG;gBAAEC;cAAM,CAAC,CAAC,CAAC;cACjGZ,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEM,gBAAgB,CAAC;cACtEjB,aAAa,CAACiB,gBAAgB,CAAC;YACjC,CAAC,MAAM;cACLP,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;cACvDX,aAAa,CAAC,CACZ;gBAAEqB,GAAG,EAAE,OAAO;gBAAEC,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;cAAE,CAAC,EAChF;gBAAED,GAAG,EAAE,UAAU;gBAAEC,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI;cAAE,CAAC,CAC3C,CAAC;YACJ;UACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;YACdK,aAAa,CAAC,CACZ;cAAEqB,GAAG,EAAE,OAAO;cAAEC,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;YAAE,CAAC,EAChF;cAAED,GAAG,EAAE,UAAU;cAAEC,KAAK,EAAE,CAAC,MAAM,EAAE,IAAI;YAAE,CAAC,CAC3C,CAAC;UACJ;QACF,CAAC;QAEDC,WAAW,CAAC,CAAC;MACf;IACF;EACF,CAAC,EAAE,CAAC1B,OAAO,EAAEJ,EAAE,CAAC,CAAC;EAEjB,MAAMuC,kBAAkB,GAAGA,CAACC,YAAY,EAAEC,WAAW,KAAK;IACxDhC,kBAAkB,CAAEiC,IAAI,KAAM;MAC5B,GAAGA,IAAI;MACP,CAACF,YAAY,GAAGC;IAClB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,WAAW,GAAIC,GAAG,IAAK;IAC3B,OAAOA,GAAG,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;EAC1E,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAAA,IAAAC,gBAAA;IAC5B,MAAMC,SAAS,GAAG7C,OAAO,aAAPA,OAAO,wBAAA4C,gBAAA,GAAP5C,OAAO,CAAEA,OAAO,cAAA4C,gBAAA,uBAAhBA,gBAAA,CAAkBhD,EAAE;IAEtC,IAAI,CAACiD,SAAS,EAAE;MACdC,KAAK,CAAC,2CAA2C,CAAC;MAClD;IACF;IAEAtC,SAAS,CAAC;MACRP,SAAS,EAAE;QACT4C,SAAS,EAAEA,SAAS;QACpBE,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;EACJ,CAAC;EAED,IAAIlD,OAAO,EAAE,oBAAOJ,OAAA;IAAAuD,QAAA,EAAG;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACrC,IAAItD,KAAK,EAAE,oBAAOL,OAAA;IAAAuD,QAAA,GAAG,SAAO,EAAClD,KAAK,CAACuD,OAAO;EAAA;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;EAE/C,IAAI,CAACpD,OAAO,CAACA,OAAO,EAAE,oBAAOP,OAAA;IAAAuD,QAAA,EAAG;EAAiB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EAErD,oBACE3D,OAAA;IAAK6D,SAAS,EAAC,cAAc;IAAAN,QAAA,gBAC3BvD,OAAA;MAAK,eAAY,iBAAiB;MAAC6D,SAAS,EAAC,SAAS;MAAAN,QAAA,eACpDvD,OAAA;QAAK6D,SAAS,EAAC,YAAY;QAAAN,QAAA,eACzBvD,OAAA;UAAK6D,SAAS,EAAC,qBAAqB;UAACC,GAAG,EAAEvD,OAAO,CAACA,OAAO,CAACwD,SAAU;UAACC,GAAG,EAAEzD,OAAO,CAACA,OAAO,CAAC0D;QAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3D,OAAA;MAAK,eAAY,qBAAqB;MAAC6D,SAAS,EAAC,SAAS;MAAAN,QAAA,gBACxDvD,OAAA;QAAI6D,SAAS,EAAC,cAAc;QAAAN,QAAA,EAAEhD,OAAO,CAACA,OAAO,CAAC0D;MAAI;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxD3D,OAAA;QAAG6D,SAAS,EAAC,eAAe;QAAAN,QAAA,EAAEhD,OAAO,CAACA,OAAO,CAAC2D;MAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxD3D,OAAA;QAAAuD,QAAA,EACK9C,iBAAiB,IAAIA,iBAAiB,CAAC0D,MAAM,GAAG,CAAC,GAChD1D,iBAAiB,CAACqB,GAAG,CAAEsC,SAAS,iBAC9BpE,OAAA;UAAK6D,SAAS,EAAC,WAAW;UAAwB,eAAa,qBAAqBf,WAAW,CAACsB,SAAS,CAAC,KAAK,CAAC,CAAC,EAAG;UAAAb,QAAA,gBAClHvD,OAAA;YAAG6D,SAAS,EAAC,gBAAgB;YAAAN,QAAA,GAAEa,SAAS,CAAC,KAAK,CAAC,EAAC,GAAC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrD3D,OAAA;YAAK6D,SAAS,EAAC,kBAAkB;YAAAN,QAAA,EAC9Ba,SAAS,CAAC,OAAO,CAAC,IAAIA,SAAS,CAAC,OAAO,CAAC,CAACtC,GAAG,CAACuC,IAAI,iBAC9CrE,OAAA;cAEA6D,SAAS,EAAE,uBAAuBlD,eAAe,IAAIA,eAAe,CAACyD,SAAS,CAAC,KAAK,CAAC,CAAC,KAAKC,IAAI,GAAG,UAAU,GAAG,EAAE,EAAG;cACpHC,OAAO,EAAEA,CAAA,KAAK;gBAAC,IAAIF,SAAS,CAAC,KAAK,CAAC,EAAE1B,kBAAkB,CAAC0B,SAAS,CAAC,KAAK,CAAC,EAAEC,IAAI,CAAC;cAAA,CAAE;cACjFE,KAAK,EAAE;gBAACC,eAAe,EAAEJ,SAAS,CAAC,KAAK,CAAC,KAAK,OAAO,GAAGC,IAAI,GAAG;cAAE,CAAE;cAAAd,QAAA,EAElEa,SAAS,CAAC,KAAK,CAAC,KAAK,OAAO,GAAG,EAAE,GAAGC;YAAI,GALpCA,IAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMH,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEC,CAAC;QAAA,GAdwBS,SAAS,CAAC,KAAK,CAAC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAe3C,CACN,CAAC,gBAEF3D,OAAA;UAAAuD,QAAA,EAAG;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAChD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACR3D,OAAA;QAAK6D,SAAS,EAAC,OAAO;QAAAN,QAAA,eACtBvD,OAAA;UAAG6D,SAAS,EAAC,eAAe;UAAAN,QAAA,GAAEhD,OAAO,CAACA,OAAO,CAACkE,MAAM,EAAC,GAAC;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACN3D,OAAA;QAAAuD,QAAA,gBACGvD,OAAA,CAACV,MAAM;UAAC,eAAY,aAAa;UAACuE,SAAS,EAAC,aAAa;UAACa,IAAI,EAAC,aAAa;UAACJ,OAAO,EAAEpB;QAAgB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxG3D,OAAA;UAAK6D,SAAS,EAAC,aAAa;UAAAN,QAAA,EACzBhD,OAAO,CAACA,OAAO,CAACoE,WAAW,GAAG7E,KAAK,CAACS,OAAO,CAACA,OAAO,CAACoE,WAAW,CAAC,gBAAG3E,OAAA;YAAAuD,QAAA,EAAG;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACzD,EAAA,CAjKQD,cAAc;EAAA,QACNL,SAAS,EACkBL,QAAQ,EAKfM,QAAQ,EAEvBL,WAAW;AAAA;AAAAoF,EAAA,GATxB3E,cAAc;AAmKvB,eAAeA,cAAc;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}