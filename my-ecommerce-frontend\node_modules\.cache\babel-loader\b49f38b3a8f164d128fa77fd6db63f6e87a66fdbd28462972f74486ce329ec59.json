{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\ProductList.js\",\n  _s = $RefreshSig$();\n// src/components/ProductList.js\nimport { useQuery, useMutation } from '@apollo/client';\nimport { GET_PRODUCTS, GET_CART_QUERY } from '../graphql/queries';\nimport { ADD_TO_CART_MUTATION } from '../graphql/mutations';\nimport { Link } from 'react-router-dom';\nimport { useToast } from '../contexts/ToastContext';\nimport cart from '../assets/img/Vector.svg';\nimport '../styles/ProductList.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction ProductList({\n  category_id,\n  category\n}) {\n  _s();\n  const {\n    loading,\n    error,\n    data\n  } = useQuery(GET_PRODUCTS);\n  const {\n    showSuccess,\n    showError\n  } = useToast();\n  const [addToCart] = useMutation(ADD_TO_CART_MUTATION, {\n    refetchQueries: [{\n      query: GET_CART_QUERY\n    }],\n    awaitRefetchQueries: true,\n    onCompleted: () => {\n      showSuccess('Product added to cart!');\n    },\n    onError: error => {\n      console.error('Add to cart error:', error);\n      showError('Failed to add product to cart. Please try again.');\n    },\n    errorPolicy: 'all'\n  });\n  if (loading) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 23\n  }, this);\n  if (error) return /*#__PURE__*/_jsxDEV(\"p\", {\n    children: [\"Error: \", error.message]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 21\n  }, this);\n  const products = category ? data.products.filter(product => product.category_id === category_id) : data.products;\n\n  // Helper function to convert product name to kebab case\n  const toKebabCase = str => {\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n  };\n\n  // Handle quick shop (add to cart with default options)\n  const handleQuickShop = (e, productId) => {\n    e.preventDefault(); // Prevent navigation to product details\n    e.stopPropagation(); // Stop event bubbling\n\n    addToCart({\n      variables: {\n        productId: productId,\n        quantity: 1\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"products\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          children: category ? category : \"All\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 7\n      }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-card\",\n        \"data-testid\": `product-${toKebabCase(product.name)}`,\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          className: \"product-link\",\n          to: `/product/${product.id}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-container\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              className: \"product-card__image\",\n              src: product.image_url,\n              alt: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"product-card__brand\",\n            children: product.brand\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-name\",\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price\",\n            children: [\"$\", product.amount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"quick-shop-btn\",\n          onClick: e => handleQuickShop(e, product.id),\n          \"data-testid\": `add-to-cart-${toKebabCase(product.name)}`,\n          title: \"Quick Shop\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: cart,\n            width: \"20\",\n            height: \"20\",\n            alt: \"Add to Cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this)]\n      }, product.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 5\n    }, this)\n  }, void 0, false);\n}\n_s(ProductList, \"mkJdYKOCjsrxblzDTtkh3KjRDjs=\", false, function () {\n  return [useQuery, useToast, useMutation];\n});\n_c = ProductList;\nexport default ProductList;\nvar _c;\n$RefreshReg$(_c, \"ProductList\");", "map": {"version": 3, "names": ["useQuery", "useMutation", "GET_PRODUCTS", "GET_CART_QUERY", "ADD_TO_CART_MUTATION", "Link", "useToast", "cart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductList", "category_id", "category", "_s", "loading", "error", "data", "showSuccess", "showError", "addToCart", "refetchQueries", "query", "awaitRefetchQueries", "onCompleted", "onError", "console", "errorPolicy", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "products", "filter", "product", "toKebabCase", "str", "toLowerCase", "replace", "handleQuickShop", "e", "productId", "preventDefault", "stopPropagation", "variables", "quantity", "className", "map", "name", "to", "id", "src", "image_url", "alt", "brand", "amount", "onClick", "title", "width", "height", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/ProductList.js"], "sourcesContent": ["// src/components/ProductList.js\r\nimport { useQuery, useMutation } from '@apollo/client';\r\nimport { GET_PRODUCTS, GET_CART_QUERY } from '../graphql/queries';\r\nimport { ADD_TO_CART_MUTATION } from '../graphql/mutations';\r\nimport { Link } from 'react-router-dom';\r\nimport { useToast } from '../contexts/ToastContext';\r\nimport cart from '../assets/img/Vector.svg';\r\nimport '../styles/ProductList.css';\r\n\r\nfunction ProductList({ category_id, category }) {\r\n  const { loading, error, data } = useQuery(GET_PRODUCTS);\r\n  const { showSuccess, showError } = useToast();\r\n\r\n  const [addToCart] = useMutation(ADD_TO_CART_MUTATION, {\r\n    refetchQueries: [{ query: GET_CART_QUERY }],\r\n    awaitRefetchQueries: true,\r\n    onCompleted: () => {\r\n      showSuccess('Product added to cart!');\r\n    },\r\n    onError: (error) => {\r\n      console.error('Add to cart error:', error);\r\n      showError('Failed to add product to cart. Please try again.');\r\n    },\r\n    errorPolicy: 'all'\r\n  });\r\n\r\n  if (loading) return <p>Loading...</p>;\r\n  if (error) return <p>Error: {error.message}</p>;\r\n\r\n  const products = category\r\n    ? data.products.filter(product => product.category_id === category_id)\r\n    : data.products;\r\n\r\n  // Helper function to convert product name to kebab case\r\n  const toKebabCase = (str) => {\r\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\r\n  };\r\n\r\n  // Handle quick shop (add to cart with default options)\r\n  const handleQuickShop = (e, productId) => {\r\n    e.preventDefault(); // Prevent navigation to product details\r\n    e.stopPropagation(); // Stop event bubbling\r\n\r\n    addToCart({\r\n      variables: {\r\n        productId: productId,\r\n        quantity: 1,\r\n      },\r\n    });\r\n  };\r\n\r\n  return (\r\n    <>\r\n    <div className=\"products\">\r\n      <div className=\"header\">\r\n        <h1>{category ? category: \"All\"}</h1>\r\n      </div>\r\n        {products.map((product) => (\r\n          <div className='product-card' data-testid={`product-${toKebabCase(product.name)}`} key={product.id}>\r\n            <Link className='product-link' to={`/product/${product.id}`}>\r\n              <div className=\"image-container\">\r\n                <img className=\"product-card__image\" src={product.image_url} alt={product.name} />\r\n              </div>\r\n              <p className=\"product-card__brand\">{product.brand}</p>\r\n              <div className=\"product-name\">{product.name}</div>\r\n              <div className=\"price\">\r\n                ${product.amount}\r\n              </div>\r\n            </Link>\r\n\r\n            {/* Quick Shop Button - only visible on hover */}\r\n            <button\r\n              className=\"quick-shop-btn\"\r\n              onClick={(e) => handleQuickShop(e, product.id)}\r\n              data-testid={`add-to-cart-${toKebabCase(product.name)}`}\r\n              title=\"Quick Shop\"\r\n            >\r\n              <img src={cart} width=\"20\" height=\"20\" alt=\"Add to Cart\" />\r\n            </button>\r\n          </div>\r\n        ))}\r\n    </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default ProductList;\r\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,gBAAgB;AACtD,SAASC,YAAY,EAAEC,cAAc,QAAQ,oBAAoB;AACjE,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnC,SAASC,WAAWA,CAAC;EAAEC,WAAW;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAC9C,MAAM;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGlB,QAAQ,CAACE,YAAY,CAAC;EACvD,MAAM;IAAEiB,WAAW;IAAEC;EAAU,CAAC,GAAGd,QAAQ,CAAC,CAAC;EAE7C,MAAM,CAACe,SAAS,CAAC,GAAGpB,WAAW,CAACG,oBAAoB,EAAE;IACpDkB,cAAc,EAAE,CAAC;MAAEC,KAAK,EAAEpB;IAAe,CAAC,CAAC;IAC3CqB,mBAAmB,EAAE,IAAI;IACzBC,WAAW,EAAEA,CAAA,KAAM;MACjBN,WAAW,CAAC,wBAAwB,CAAC;IACvC,CAAC;IACDO,OAAO,EAAGT,KAAK,IAAK;MAClBU,OAAO,CAACV,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CG,SAAS,CAAC,kDAAkD,CAAC;IAC/D,CAAC;IACDQ,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,IAAIZ,OAAO,EAAE,oBAAOP,OAAA;IAAAoB,QAAA,EAAG;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;EACrC,IAAIhB,KAAK,EAAE,oBAAOR,OAAA;IAAAoB,QAAA,GAAG,SAAO,EAACZ,KAAK,CAACiB,OAAO;EAAA;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;EAE/C,MAAME,QAAQ,GAAGrB,QAAQ,GACrBI,IAAI,CAACiB,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACxB,WAAW,KAAKA,WAAW,CAAC,GACpEK,IAAI,CAACiB,QAAQ;;EAEjB;EACA,MAAMG,WAAW,GAAIC,GAAG,IAAK;IAC3B,OAAOA,GAAG,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;EAC1E,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,CAAC,EAAEC,SAAS,KAAK;IACxCD,CAAC,CAACE,cAAc,CAAC,CAAC,CAAC,CAAC;IACpBF,CAAC,CAACG,eAAe,CAAC,CAAC,CAAC,CAAC;;IAErBzB,SAAS,CAAC;MACR0B,SAAS,EAAE;QACTH,SAAS,EAAEA,SAAS;QACpBI,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;EACJ,CAAC;EAED,oBACEvC,OAAA,CAAAE,SAAA;IAAAkB,QAAA,eACApB,OAAA;MAAKwC,SAAS,EAAC,UAAU;MAAApB,QAAA,gBACvBpB,OAAA;QAAKwC,SAAS,EAAC,QAAQ;QAAApB,QAAA,eACrBpB,OAAA;UAAAoB,QAAA,EAAKf,QAAQ,GAAGA,QAAQ,GAAE;QAAK;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,EACHE,QAAQ,CAACe,GAAG,CAAEb,OAAO,iBACpB5B,OAAA;QAAKwC,SAAS,EAAC,cAAc;QAAC,eAAa,WAAWX,WAAW,CAACD,OAAO,CAACc,IAAI,CAAC,EAAG;QAAAtB,QAAA,gBAChFpB,OAAA,CAACJ,IAAI;UAAC4C,SAAS,EAAC,cAAc;UAACG,EAAE,EAAE,YAAYf,OAAO,CAACgB,EAAE,EAAG;UAAAxB,QAAA,gBAC1DpB,OAAA;YAAKwC,SAAS,EAAC,iBAAiB;YAAApB,QAAA,eAC9BpB,OAAA;cAAKwC,SAAS,EAAC,qBAAqB;cAACK,GAAG,EAAEjB,OAAO,CAACkB,SAAU;cAACC,GAAG,EAAEnB,OAAO,CAACc;YAAK;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC,eACNxB,OAAA;YAAGwC,SAAS,EAAC,qBAAqB;YAAApB,QAAA,EAAEQ,OAAO,CAACoB;UAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDxB,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAApB,QAAA,EAAEQ,OAAO,CAACc;UAAI;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDxB,OAAA;YAAKwC,SAAS,EAAC,OAAO;YAAApB,QAAA,GAAC,GACpB,EAACQ,OAAO,CAACqB,MAAM;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPxB,OAAA;UACEwC,SAAS,EAAC,gBAAgB;UAC1BU,OAAO,EAAGhB,CAAC,IAAKD,eAAe,CAACC,CAAC,EAAEN,OAAO,CAACgB,EAAE,CAAE;UAC/C,eAAa,eAAef,WAAW,CAACD,OAAO,CAACc,IAAI,CAAC,EAAG;UACxDS,KAAK,EAAC,YAAY;UAAA/B,QAAA,eAElBpB,OAAA;YAAK6C,GAAG,EAAE/C,IAAK;YAACsD,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC,IAAI;YAACN,GAAG,EAAC;UAAa;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC;MAAA,GApB6EI,OAAO,CAACgB,EAAE;QAAAvB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqB7F,CACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC,gBACJ,CAAC;AAEP;AAAClB,EAAA,CA3EQH,WAAW;EAAA,QACeZ,QAAQ,EACNM,QAAQ,EAEvBL,WAAW;AAAA;AAAA8D,EAAA,GAJxBnD,WAAW;AA6EpB,eAAeA,WAAW;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}