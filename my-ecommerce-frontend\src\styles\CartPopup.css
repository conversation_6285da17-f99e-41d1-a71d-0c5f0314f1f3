/* Combined Styles from CartPopup.css and Modal.css */

/* Modal Background Styles */
.modalBackground {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  right: 0;
  top: 70px;
  overflow-y: auto;
}

/* Modal Container Styles */
.modalContainer {
  width: 360px;
  max-height: 580px;
  background-color: white;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 5px 25px;
  display: flex;
  flex-direction: column;
  padding: 15px;
  position: fixed;
  right: 33px;
  top: 70px;
  border-radius: 0;
  overflow-y: auto;
  min-height: 500px; /* Ensure consistent minimum height */
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 0;
  border-bottom: none;
}

.cart-header h2 {
  margin: 0;
  font-size: 1.1rem;
  color: #1d1f22;
  font-weight: 700;
  font-family: 'Raleway', sans-serif;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background-color: #f5f5f5;
  color: #333;
}

.modalContainer .title {
  text-align: center;
  margin-top: 10px;
  font-size: 1.8rem;
  color: #333;
}

.titleCloseBtn {
  display: flex;
  justify-content: flex-end;
}

.titleCloseBtn button {
  background-color: transparent;
  border: none;
  font-size: 25px;
  cursor: pointer;
}

/* Cart Popup Styles */
.cart-popup {
  width: 100%;
  background: #fff;
  padding: 1.5rem;
  overflow-y: auto;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2.5rem;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 2.5rem;
  padding-top: 0;
  align-items: flex-start;
  gap: 20px;
}

.cart-item-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cart-item-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15px;
}

.image-quantity-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.cart-item-image {
  flex-shrink: 0;
}

.cart-item-image img {
  width: 120px;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
}

.product-name {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d1f22;
  line-height: 1.3;
  font-family: 'Raleway', sans-serif;
}

.product-price {
  margin: 0;
  font-size: 1rem;
  color: #1d1f22;
  font-weight: 600;
  font-family: 'Raleway', sans-serif;
}

/* Size Section */
.size-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.size-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
}

.size-options {
  display: flex;
  gap: 8px;
}

.size-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #1d1f22;
  background: #fff;
  color: #1d1f22;
  font-size: 0.85rem;
  font-weight: 400;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Source Sans Pro', sans-serif;
  transition: all 0.2s ease;
}

.size-btn:hover {
  background: #f5f5f5;
}

.size-btn.selected {
  background: #1d1f22;
  color: #fff;
}

.size-btn.non-clickable {
  cursor: default;
  pointer-events: none;
}

.size-btn.non-clickable:hover {
  background: inherit;
}

/* Color Section */
.color-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.color-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
}

.color-options {
  display: flex;
  gap: 8px;
}

.color-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid #ccc;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.color-circle:hover {
  transform: scale(1.1);
}

.color-circle.selected {
  border: 2px solid #1d1f22;
  box-shadow: 0 0 0 2px #fff, 0 0 0 4px #1d1f22;
}

.color-circle.non-clickable {
  cursor: default;
  pointer-events: none;
}

.color-circle.non-clickable:hover {
  transform: none;
}

/* Quantity Section */
.quantity-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.cart-item-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0;
  border: none;
}

.quantity-btn {
  width: 1.75rem; /* w-7 equivalent */
  height: 1.75rem; /* h-7 equivalent */
  border: 1px solid #000;
  background: #fff;
  color: #000;
  cursor: pointer;
  font-size: 1.125rem; /* text-lg equivalent */
  font-weight: 600; /* font-semibold equivalent */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-family: 'Raleway', sans-serif;
  border-radius: 0.125rem; /* rounded-sm equivalent */
  box-shadow: none;
}

.quantity-btn:hover {
  background: #f5f5f5;
}

.quantity-btn:active {
  background: #e0e0e0;
}

.quantity-display {
  min-width: 1.75rem; /* w-7 equivalent */
  height: 1.75rem; /* h-7 equivalent */
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-weight: 500;
  color: #1d1f22;
  background: #fff;
  border: none;
  font-family: 'Raleway', sans-serif;
  font-size: 1.125rem; /* text-lg equivalent */
}

.item-number {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
}

/* Total Section */
.total-section {
  display: flex;
  justify-content: space-between;
  font-size: 1.1rem;
  font-weight: 600;
  margin-top: 30px;
  margin-bottom: 20px;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
}

.total-section p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.place-order-btn {
  display: block;
  width: 100%;
  margin-top: 1rem;
  padding: 16px;
  background: #5ECE7B;
  color: #fff;
  text-align: center;
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Raleway', sans-serif;
  transition: background-color 0.2s ease;
}

.place-order-btn:hover {
  background: #4CAF50;
}

/* Cart Content Area - ensures consistent layout */
.cart-content {
  display: flex;
  flex-direction: column;
  min-height: 200px; /* Minimum height for consistency */
  flex: 1; /* Take up available space */
}

.cart-items-container {
  flex: 1; /* Allow items to take up available space */
  margin-bottom: 1rem;
}

/* Ensure last cart item doesn't have bottom border */
.cart-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 1rem;
}

/* Footer Buttons in Modal */
.modalContainer .footer button {
  width: 150px;
  height: 45px;
  margin: 10px;
  border: none;
  background-color: cornflowerblue;
  color: white;
  border-radius: 8px;
  font-size: 20px;
  cursor: pointer;
}

#cancelBtn {
  background-color: crimson;
}
