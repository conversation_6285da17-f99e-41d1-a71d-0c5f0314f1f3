{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\App.js\";\nimport Navbar from './components/Navbar';\nimport { BrowserRouter as Router, Route, Routes } from 'react-router-dom';\nimport ProductList from './components/ProductList';\nimport CartPopup from './components/CartPopup';\nimport ProductDetails from './components/ProductDetails';\nimport { ToastProvider } from './contexts/ToastContext';\nimport 'typeface-raleway';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ToastProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(ProductList, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/product/:id\",\n          element: /*#__PURE__*/_jsxDEV(ProductDetails, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/category/clothes\",\n          element: /*#__PURE__*/_jsxDEV(ProductList, {\n            category_id: 14,\n            category: \"clothes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/category/tech\",\n          element: /*#__PURE__*/_jsxDEV(ProductList, {\n            category_id: 15,\n            category: \"tech\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cart\",\n          element: /*#__PURE__*/_jsxDEV(CartPopup, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "ProductList", "CartPopup", "ProductDetails", "ToastProvider", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "category_id", "category", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/App.js"], "sourcesContent": ["\nimport Navbar from './components/Navbar';\nimport { BrowserRouter as Router, Route, Routes } from 'react-router-dom';\nimport ProductList from './components/ProductList';\nimport CartPopup from './components/CartPopup';\nimport ProductDetails from './components/ProductDetails';\nimport { ToastProvider } from './contexts/ToastContext';\nimport 'typeface-raleway';\n\n\nfunction App() {\n  return (\n    <ToastProvider>\n      <Router>\n        <Navbar></Navbar>\n        <Routes>\n          <Route path=\"/\" element={<ProductList />} />\n          <Route path=\"/product/:id\" element={<ProductDetails />} />\n          <Route path=\"/category/clothes\" element={<ProductList category_id={14} category=\"clothes\" />} />\n          <Route path=\"/category/tech\" element={<ProductList category_id={15} category=\"tech\" />} />\n          <Route path=\"/cart\" element={<CartPopup />} />\n        </Routes>\n      </Router>\n    </ToastProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AACA,OAAOA,MAAM,MAAM,qBAAqB;AACxC,SAASC,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AACzE,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1B,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACF,aAAa;IAAAI,QAAA,eACZF,OAAA,CAACR,MAAM;MAAAU,QAAA,gBACLF,OAAA,CAACV,MAAM;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACjBN,OAAA,CAACN,MAAM;QAAAQ,QAAA,gBACLF,OAAA,CAACP,KAAK;UAACc,IAAI,EAAC,GAAG;UAACC,OAAO,eAAER,OAAA,CAACL,WAAW;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CN,OAAA,CAACP,KAAK;UAACc,IAAI,EAAC,cAAc;UAACC,OAAO,eAAER,OAAA,CAACH,cAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DN,OAAA,CAACP,KAAK;UAACc,IAAI,EAAC,mBAAmB;UAACC,OAAO,eAAER,OAAA,CAACL,WAAW;YAACc,WAAW,EAAE,EAAG;YAACC,QAAQ,EAAC;UAAS;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChGN,OAAA,CAACP,KAAK;UAACc,IAAI,EAAC,gBAAgB;UAACC,OAAO,eAAER,OAAA,CAACL,WAAW;YAACc,WAAW,EAAE,EAAG;YAACC,QAAQ,EAAC;UAAM;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1FN,OAAA,CAACP,KAAK;UAACc,IAAI,EAAC,OAAO;UAACC,OAAO,eAAER,OAAA,CAACJ,SAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACK,EAAA,GAfQV,GAAG;AAiBZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}