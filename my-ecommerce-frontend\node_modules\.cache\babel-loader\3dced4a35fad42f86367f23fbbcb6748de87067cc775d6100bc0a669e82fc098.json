{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\contexts\\\\ToastContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { createContext, useContext, useState } from 'react';\nimport Toast from '../components/Toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ToastContext = /*#__PURE__*/createContext();\nexport const useToast = () => {\n  _s();\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n_s(useToast, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ToastProvider = ({\n  children\n}) => {\n  _s2();\n  const [toasts, setToasts] = useState([]);\n  const showToast = (message, type = 'success', duration = 3000) => {\n    const id = Date.now() + Math.random();\n    const newToast = {\n      id,\n      message,\n      type,\n      duration\n    };\n    setToasts(prev => [...prev, newToast]);\n  };\n  const removeToast = id => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n  const showSuccess = (message, duration) => showToast(message, 'success', duration);\n  const showError = (message, duration) => showToast(message, 'error', duration);\n  const showWarning = (message, duration) => showToast(message, 'warning', duration);\n  const showInfo = (message, duration) => showToast(message, 'info', duration);\n  return /*#__PURE__*/_jsxDEV(ToastContext.Provider, {\n    value: {\n      showToast,\n      showSuccess,\n      showError,\n      showWarning,\n      showInfo\n    },\n    children: [children, /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"toast-container\",\n      children: toasts.map(toast => /*#__PURE__*/_jsxDEV(Toast, {\n        message: toast.message,\n        type: toast.type,\n        duration: toast.duration,\n        onClose: () => removeToast(toast.id)\n      }, toast.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s2(ToastProvider, \"nD8TBOiFYf9ajstmZpZK2DP4rNo=\");\n_c = ToastProvider;\nvar _c;\n$RefreshReg$(_c, \"ToastProvider\");", "map": {"version": 3, "names": ["createContext", "useContext", "useState", "Toast", "jsxDEV", "_jsxDEV", "ToastContext", "useToast", "_s", "context", "Error", "ToastProvider", "children", "_s2", "toasts", "setToasts", "showToast", "message", "type", "duration", "id", "Date", "now", "Math", "random", "newToast", "prev", "removeToast", "filter", "toast", "showSuccess", "showError", "showWarning", "showInfo", "Provider", "value", "className", "map", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/contexts/ToastContext.js"], "sourcesContent": ["import { createContext, useContext, useState } from 'react';\nimport Toast from '../components/Toast';\n\nconst ToastContext = createContext();\n\nexport const useToast = () => {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n\nexport const ToastProvider = ({ children }) => {\n  const [toasts, setToasts] = useState([]);\n\n  const showToast = (message, type = 'success', duration = 3000) => {\n    const id = Date.now() + Math.random();\n    const newToast = { id, message, type, duration };\n    \n    setToasts(prev => [...prev, newToast]);\n  };\n\n  const removeToast = (id) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n\n  const showSuccess = (message, duration) => showToast(message, 'success', duration);\n  const showError = (message, duration) => showToast(message, 'error', duration);\n  const showWarning = (message, duration) => showToast(message, 'warning', duration);\n  const showInfo = (message, duration) => showToast(message, 'info', duration);\n\n  return (\n    <ToastContext.Provider value={{ \n      showToast, \n      showSuccess, \n      showError, \n      showWarning, \n      showInfo \n    }}>\n      {children}\n      <div className=\"toast-container\">\n        {toasts.map(toast => (\n          <Toast\n            key={toast.id}\n            message={toast.message}\n            type={toast.type}\n            duration={toast.duration}\n            onClose={() => removeToast(toast.id)}\n          />\n        ))}\n      </div>\n    </ToastContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AAC3D,OAAOC,KAAK,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,YAAY,gBAAGN,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMO,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGR,UAAU,CAACK,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAQrB,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAMc,SAAS,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,SAAS,EAAEC,QAAQ,GAAG,IAAI,KAAK;IAChE,MAAMC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;IACrC,MAAMC,QAAQ,GAAG;MAAEL,EAAE;MAAEH,OAAO;MAAEC,IAAI;MAAEC;IAAS,CAAC;IAEhDJ,SAAS,CAACW,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,QAAQ,CAAC,CAAC;EACxC,CAAC;EAED,MAAME,WAAW,GAAIP,EAAE,IAAK;IAC1BL,SAAS,CAACW,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACT,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC1D,CAAC;EAED,MAAMU,WAAW,GAAGA,CAACb,OAAO,EAAEE,QAAQ,KAAKH,SAAS,CAACC,OAAO,EAAE,SAAS,EAAEE,QAAQ,CAAC;EAClF,MAAMY,SAAS,GAAGA,CAACd,OAAO,EAAEE,QAAQ,KAAKH,SAAS,CAACC,OAAO,EAAE,OAAO,EAAEE,QAAQ,CAAC;EAC9E,MAAMa,WAAW,GAAGA,CAACf,OAAO,EAAEE,QAAQ,KAAKH,SAAS,CAACC,OAAO,EAAE,SAAS,EAAEE,QAAQ,CAAC;EAClF,MAAMc,QAAQ,GAAGA,CAAChB,OAAO,EAAEE,QAAQ,KAAKH,SAAS,CAACC,OAAO,EAAE,MAAM,EAAEE,QAAQ,CAAC;EAE5E,oBACEd,OAAA,CAACC,YAAY,CAAC4B,QAAQ;IAACC,KAAK,EAAE;MAC5BnB,SAAS;MACTc,WAAW;MACXC,SAAS;MACTC,WAAW;MACXC;IACF,CAAE;IAAArB,QAAA,GACCA,QAAQ,eACTP,OAAA;MAAK+B,SAAS,EAAC,iBAAiB;MAAAxB,QAAA,EAC7BE,MAAM,CAACuB,GAAG,CAACR,KAAK,iBACfxB,OAAA,CAACF,KAAK;QAEJc,OAAO,EAAEY,KAAK,CAACZ,OAAQ;QACvBC,IAAI,EAAEW,KAAK,CAACX,IAAK;QACjBC,QAAQ,EAAEU,KAAK,CAACV,QAAS;QACzBmB,OAAO,EAAEA,CAAA,KAAMX,WAAW,CAACE,KAAK,CAACT,EAAE;MAAE,GAJhCS,KAAK,CAACT,EAAE;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKd,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACe,CAAC;AAE5B,CAAC;AAAC7B,GAAA,CAzCWF,aAAa;AAAAgC,EAAA,GAAbhC,aAAa;AAAA,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}